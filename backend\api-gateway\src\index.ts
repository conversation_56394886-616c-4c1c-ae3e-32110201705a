import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import morgan from 'morgan';
import rateLimit from 'express-rate-limit';
import { createServer } from 'http';
import { Server as SocketIOServer } from 'socket.io';
import swaggerUi from 'swagger-ui-express';
import swaggerJsdoc from 'swagger-jsdoc';
import 'express-async-errors';

import { config } from './config/config';
import { logger } from './utils/logger';
import { errorHandler } from './middleware/errorHandler';
import { authMiddleware } from './middleware/auth';
import { requestLogger } from './middleware/requestLogger';
import { healthCheck } from './middleware/healthCheck';
import { setupProxyRoutes } from './routes/proxy';
import { setupWebSocketHandlers } from './services/websocket';
import { RedisService } from './services/redis';
import { MetricsService } from './services/metrics';

class APIGateway {
  private app: express.Application;
  private server: any;
  private io: SocketIOServer;
  private redisService: RedisService;
  private metricsService: MetricsService;

  constructor() {
    this.app = express();
    this.server = createServer(this.app);
    this.io = new SocketIOServer(this.server, {
      cors: {
        origin: config.cors.origin,
        methods: ['GET', 'POST'],
        credentials: true,
      },
    });
    this.redisService = new RedisService();
    this.metricsService = new MetricsService();
  }

  private setupMiddleware(): void {
    // Security middleware
    this.app.use(helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          scriptSrc: ["'self'"],
          imgSrc: ["'self'", "data:", "https:"],
        },
      },
    }));

    // CORS configuration
    this.app.use(cors({
      origin: config.cors.origin,
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    }));

    // Compression
    this.app.use(compression());

    // Request parsing
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Logging
    this.app.use(morgan('combined', {
      stream: { write: (message) => logger.info(message.trim()) },
    }));
    this.app.use(requestLogger);

    // Rate limiting
    const limiter = rateLimit({
      windowMs: config.rateLimit.windowMs,
      max: config.rateLimit.maxRequests,
      message: {
        error: 'Too many requests from this IP, please try again later.',
      },
      standardHeaders: true,
      legacyHeaders: false,
      store: new (require('rate-limit-redis'))({
        client: this.redisService.getClient(),
        prefix: 'rl:',
      }),
    });
    this.app.use('/api', limiter);

    // Metrics collection
    this.app.use(this.metricsService.collectMetrics.bind(this.metricsService));
  }

  private setupSwagger(): void {
    const swaggerOptions = {
      definition: {
        openapi: '3.0.0',
        info: {
          title: 'Accly API Gateway',
          version: '1.0.0',
          description: 'API Gateway for Accly Accounting Services Platform',
          contact: {
            name: 'Accly Development Team',
            email: '<EMAIL>',
          },
        },
        servers: [
          {
            url: `http://localhost:${config.port}`,
            description: 'Development server',
          },
        ],
        components: {
          securitySchemes: {
            bearerAuth: {
              type: 'http',
              scheme: 'bearer',
              bearerFormat: 'JWT',
            },
          },
        },
        security: [
          {
            bearerAuth: [],
          },
        ],
      },
      apis: ['./src/routes/*.ts', './src/middleware/*.ts'],
    };

    const swaggerSpec = swaggerJsdoc(swaggerOptions);
    this.app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerSpec));
  }

  private setupRoutes(): void {
    // Health check endpoint
    this.app.use('/health', healthCheck);

    // API routes with authentication
    this.app.use('/api', authMiddleware);
    
    // Setup proxy routes to microservices
    setupProxyRoutes(this.app);

    // Metrics endpoint
    this.app.get('/metrics', this.metricsService.getMetrics.bind(this.metricsService));

    // 404 handler
    this.app.use('*', (req, res) => {
      res.status(404).json({
        error: 'Route not found',
        message: `Cannot ${req.method} ${req.originalUrl}`,
      });
    });
  }

  private setupWebSocket(): void {
    setupWebSocketHandlers(this.io, this.redisService);
  }

  private setupErrorHandling(): void {
    this.app.use(errorHandler);

    // Graceful shutdown
    process.on('SIGTERM', this.gracefulShutdown.bind(this));
    process.on('SIGINT', this.gracefulShutdown.bind(this));

    // Unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
      this.gracefulShutdown();
    });

    // Uncaught exceptions
    process.on('uncaughtException', (error) => {
      logger.error('Uncaught Exception:', error);
      this.gracefulShutdown();
    });
  }

  private async gracefulShutdown(): Promise<void> {
    logger.info('Starting graceful shutdown...');

    // Stop accepting new connections
    this.server.close(() => {
      logger.info('HTTP server closed');
    });

    // Close WebSocket connections
    this.io.close(() => {
      logger.info('WebSocket server closed');
    });

    // Close Redis connection
    await this.redisService.disconnect();

    // Exit process
    process.exit(0);
  }

  public async start(): Promise<void> {
    try {
      // Initialize Redis connection
      await this.redisService.connect();
      logger.info('Redis connected successfully');

      // Setup middleware
      this.setupMiddleware();

      // Setup Swagger documentation
      this.setupSwagger();

      // Setup routes
      this.setupRoutes();

      // Setup WebSocket
      this.setupWebSocket();

      // Setup error handling
      this.setupErrorHandling();

      // Start server
      this.server.listen(config.port, () => {
        logger.info(`API Gateway started on port ${config.port}`);
        logger.info(`Environment: ${config.nodeEnv}`);
        logger.info(`API Documentation: http://localhost:${config.port}/api-docs`);
      });

    } catch (error) {
      logger.error('Failed to start API Gateway:', error);
      process.exit(1);
    }
  }
}

// Start the API Gateway
const apiGateway = new APIGateway();
apiGateway.start().catch((error) => {
  logger.error('Failed to start application:', error);
  process.exit(1);
});

export default apiGateway;
