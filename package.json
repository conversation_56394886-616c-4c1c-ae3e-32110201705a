{"name": "accly-platform", "version": "1.0.0", "description": "Scalable Accounting Services Aggregator Platform", "private": true, "workspaces": ["frontend", "backend/*"], "scripts": {"dev": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend/api-gateway && npm run dev", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "npm run build:services", "build:services": "concurrently \"cd backend/api-gateway && npm run build\" \"cd backend/auth-service && npm run build\" \"cd backend/user-service && npm run build\" \"cd backend/booking-service && npm run build\" \"cd backend/payment-service && npm run build\" \"cd backend/notification-service && npm run build\" \"cd backend/analytics-service && npm run build\"", "test": "npm run test:frontend && npm run test:backend", "test:frontend": "cd frontend && npm run test", "test:backend": "npm run test:services", "test:services": "concurrently \"cd backend/api-gateway && npm run test\" \"cd backend/auth-service && npm run test\" \"cd backend/user-service && npm run test\" \"cd backend/booking-service && npm run test\" \"cd backend/payment-service && npm run test\" \"cd backend/notification-service && npm run test\" \"cd backend/analytics-service && npm run test\"", "test:e2e": "cd tests && npm run test:e2e", "test:load": "cd tests && npm run test:load", "lint": "npm run lint:frontend && npm run lint:backend", "lint:frontend": "cd frontend && npm run lint", "lint:backend": "npm run lint:services", "lint:services": "concurrently \"cd backend/api-gateway && npm run lint\" \"cd backend/auth-service && npm run lint\" \"cd backend/user-service && npm run lint\" \"cd backend/booking-service && npm run lint\" \"cd backend/payment-service && npm run lint\" \"cd backend/notification-service && npm run lint\" \"cd backend/analytics-service && npm run lint\"", "install:all": "npm install && npm run install:frontend && npm run install:backend", "install:frontend": "cd frontend && npm install", "install:backend": "npm run install:services", "install:services": "concurrently \"cd backend/api-gateway && npm install\" \"cd backend/auth-service && npm install\" \"cd backend/user-service && npm install\" \"cd backend/booking-service && npm install\" \"cd backend/payment-service && npm install\" \"cd backend/notification-service && npm install\" \"cd backend/analytics-service && npm install\"", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "k8s:deploy": "kubectl apply -f infrastructure/k8s/", "k8s:delete": "kubectl delete -f infrastructure/k8s/", "migrate:up": "cd database && npm run migrate:up", "migrate:down": "cd database && npm run migrate:down", "seed": "cd database && npm run seed", "docs:generate": "npm run docs:api && npm run docs:architecture", "docs:api": "swagger-codegen generate -i docs/api/openapi.yaml -l html2 -o docs/api/html", "docs:architecture": "cd docs && npm run build", "deploy:staging": "npm run build && npm run docker:build && npm run k8s:deploy", "deploy:production": "npm run test && npm run build && npm run docker:build && npm run k8s:deploy", "monitor:logs": "kubectl logs -f deployment/api-gateway", "monitor:metrics": "open http://localhost:3000/grafana", "backup:db": "cd database && npm run backup", "restore:db": "cd database && npm run restore"}, "devDependencies": {"concurrently": "^8.2.2", "husky": "^8.0.3", "lint-staged": "^15.2.0", "@commitlint/cli": "^18.4.3", "@commitlint/config-conventional": "^18.4.3", "swagger-codegen": "^3.0.46"}, "husky": {"hooks": {"pre-commit": "lint-staged", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md,yml,yaml}": ["prettier --write"]}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/accly-platform.git"}, "keywords": ["accounting", "services", "aggregator", "scalable", "microservices", "react", "nodejs", "typescript"], "author": "Accly Development Team", "license": "MIT"}