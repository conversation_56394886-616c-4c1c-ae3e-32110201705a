version: '3.8'

services:
  # Database Services
  postgres-primary:
    image: postgres:15-alpine
    container_name: accly-postgres-primary
    environment:
      POSTGRES_DB: accly_db
      POSTGRES_USER: accly_user
      POSTGRES_PASSWORD: accly_password
      POSTGRES_REPLICATION_USER: replicator
      POSTGRES_REPLICATION_PASSWORD: replicator_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_primary_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    command: |
      postgres 
      -c wal_level=replica 
      -c max_wal_senders=3 
      -c max_replication_slots=3
    networks:
      - accly-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U accly_user -d accly_db"]
      interval: 30s
      timeout: 10s
      retries: 3

  postgres-replica:
    image: postgres:15-alpine
    container_name: accly-postgres-replica
    environment:
      POSTGRES_DB: accly_db
      POSTGRES_USER: accly_user
      POSTGRES_PASSWORD: accly_password
      PGUSER: postgres
      POSTGRES_MASTER_SERVICE: postgres-primary
      POSTGRES_REPLICATION_USER: replicator
      POSTGRES_REPLICATION_PASSWORD: replicator_password
    ports:
      - "5433:5432"
    volumes:
      - postgres_replica_data:/var/lib/postgresql/data
    depends_on:
      postgres-primary:
        condition: service_healthy
    networks:
      - accly-network

  # Redis for caching and sessions
  redis:
    image: redis:7-alpine
    container_name: accly-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --requirepass redis_password
    networks:
      - accly-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Message Queue
  rabbitmq:
    image: rabbitmq:3-management-alpine
    container_name: accly-rabbitmq
    environment:
      RABBITMQ_DEFAULT_USER: accly_user
      RABBITMQ_DEFAULT_PASS: accly_password
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - accly-network
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Elasticsearch for search and logging
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: accly-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - accly-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # API Gateway
  api-gateway:
    build:
      context: ./backend/api-gateway
      dockerfile: Dockerfile
    container_name: accly-api-gateway
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - PORT=3000
      - POSTGRES_URL=************************************************************/accly_db
      - REDIS_URL=redis://:redis_password@redis:6379
      - RABBITMQ_URL=amqp://accly_user:accly_password@rabbitmq:5672
      - JWT_SECRET=your-super-secret-jwt-key
      - ELASTICSEARCH_URL=http://elasticsearch:9200
    depends_on:
      postgres-primary:
        condition: service_healthy
      redis:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    volumes:
      - ./backend/api-gateway:/app
      - /app/node_modules
    networks:
      - accly-network
    restart: unless-stopped

  # Auth Service
  auth-service:
    build:
      context: ./backend/auth-service
      dockerfile: Dockerfile
    container_name: accly-auth-service
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=development
      - PORT=3001
      - POSTGRES_URL=************************************************************/accly_db
      - REDIS_URL=redis://:redis_password@redis:6379
      - JWT_SECRET=your-super-secret-jwt-key
      - EMAIL_SERVICE_API_KEY=your-sendgrid-api-key
      - SMS_SERVICE_API_KEY=your-twilio-api-key
    depends_on:
      postgres-primary:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./backend/auth-service:/app
      - /app/node_modules
    networks:
      - accly-network
    restart: unless-stopped

  # User Service
  user-service:
    build:
      context: ./backend/user-service
      dockerfile: Dockerfile
    container_name: accly-user-service
    ports:
      - "3002:3002"
    environment:
      - NODE_ENV=development
      - PORT=3002
      - POSTGRES_URL=************************************************************/accly_db
      - REDIS_URL=redis://:redis_password@redis:6379
    depends_on:
      postgres-primary:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./backend/user-service:/app
      - /app/node_modules
    networks:
      - accly-network
    restart: unless-stopped

  # Booking Service
  booking-service:
    build:
      context: ./backend/booking-service
      dockerfile: Dockerfile
    container_name: accly-booking-service
    ports:
      - "3003:3003"
    environment:
      - NODE_ENV=development
      - PORT=3003
      - POSTGRES_URL=************************************************************/accly_db
      - REDIS_URL=redis://:redis_password@redis:6379
      - RABBITMQ_URL=amqp://accly_user:accly_password@rabbitmq:5672
    depends_on:
      postgres-primary:
        condition: service_healthy
      redis:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    volumes:
      - ./backend/booking-service:/app
      - /app/node_modules
    networks:
      - accly-network
    restart: unless-stopped

  # Payment Service
  payment-service:
    build:
      context: ./backend/payment-service
      dockerfile: Dockerfile
    container_name: accly-payment-service
    ports:
      - "3004:3004"
    environment:
      - NODE_ENV=development
      - PORT=3004
      - POSTGRES_URL=************************************************************/accly_db
      - REDIS_URL=redis://:redis_password@redis:6379
      - STRIPE_SECRET_KEY=your-stripe-secret-key
      - RAZORPAY_KEY_ID=your-razorpay-key-id
      - RAZORPAY_KEY_SECRET=your-razorpay-key-secret
      - PAYPAL_CLIENT_ID=your-paypal-client-id
      - PAYPAL_CLIENT_SECRET=your-paypal-client-secret
    depends_on:
      postgres-primary:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./backend/payment-service:/app
      - /app/node_modules
    networks:
      - accly-network
    restart: unless-stopped

  # Notification Service
  notification-service:
    build:
      context: ./backend/notification-service
      dockerfile: Dockerfile
    container_name: accly-notification-service
    ports:
      - "3005:3005"
    environment:
      - NODE_ENV=development
      - PORT=3005
      - POSTGRES_URL=************************************************************/accly_db
      - REDIS_URL=redis://:redis_password@redis:6379
      - RABBITMQ_URL=amqp://accly_user:accly_password@rabbitmq:5672
      - SENDGRID_API_KEY=your-sendgrid-api-key
      - TWILIO_ACCOUNT_SID=your-twilio-account-sid
      - TWILIO_AUTH_TOKEN=your-twilio-auth-token
      - FIREBASE_SERVER_KEY=your-firebase-server-key
    depends_on:
      postgres-primary:
        condition: service_healthy
      redis:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    volumes:
      - ./backend/notification-service:/app
      - /app/node_modules
    networks:
      - accly-network
    restart: unless-stopped

  # Analytics Service
  analytics-service:
    build:
      context: ./backend/analytics-service
      dockerfile: Dockerfile
    container_name: accly-analytics-service
    ports:
      - "3006:3006"
    environment:
      - NODE_ENV=development
      - PORT=3006
      - POSTGRES_URL=************************************************************/accly_db
      - REDIS_URL=redis://:redis_password@redis:6379
      - ELASTICSEARCH_URL=http://elasticsearch:9200
    depends_on:
      postgres-primary:
        condition: service_healthy
      redis:
        condition: service_healthy
      elasticsearch:
        condition: service_healthy
    volumes:
      - ./backend/analytics-service:/app
      - /app/node_modules
    networks:
      - accly-network
    restart: unless-stopped

  # Frontend Application
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: accly-frontend
    ports:
      - "3100:3000"
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_API_URL=http://localhost:3000
      - NEXT_PUBLIC_WS_URL=ws://localhost:3000
    depends_on:
      - api-gateway
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - /app/.next
    networks:
      - accly-network
    restart: unless-stopped

volumes:
  postgres_primary_data:
  postgres_replica_data:
  redis_data:
  rabbitmq_data:
  elasticsearch_data:

networks:
  accly-network:
    driver: bridge
