{"name": "accly-api-gateway", "version": "1.0.0", "description": "API Gateway for Accly Platform", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts"}, "dependencies": {"express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "cors": "^2.8.5", "compression": "^1.7.4", "morgan": "^1.10.0", "http-proxy-middleware": "^2.0.6", "jsonwebtoken": "^9.0.2", "redis": "^4.6.10", "ioredis": "^5.3.2", "socket.io": "^4.7.4", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "express-validator": "^7.0.1", "swagger-ui-express": "^5.0.0", "swagger-jsdoc": "^6.2.8", "dotenv": "^16.3.1", "axios": "^1.6.2", "uuid": "^9.0.1", "moment": "^2.29.4", "lodash": "^4.17.21", "express-async-errors": "^3.1.1", "http-status-codes": "^2.3.0", "node-cron": "^3.0.3"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/morgan": "^1.9.9", "@types/jsonwebtoken": "^9.0.5", "@types/swagger-ui-express": "^4.1.6", "@types/swagger-jsdoc": "^6.0.4", "@types/uuid": "^9.0.7", "@types/moment": "^2.13.0", "@types/lodash": "^4.14.202", "@types/node": "^20.10.4", "@types/node-cron": "^3.0.11", "@types/jest": "^29.5.8", "@types/supertest": "^2.0.16", "typescript": "^5.3.3", "nodemon": "^3.0.2", "ts-node": "^10.9.1", "jest": "^29.7.0", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "eslint": "^8.55.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "prettier": "^3.1.0"}, "engines": {"node": ">=18.0.0"}, "keywords": ["api-gateway", "microservices", "express", "typescript", "scalable"], "author": "Accly Development Team", "license": "MIT"}