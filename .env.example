# Database Configuration
POSTGRES_URL=postgresql://accly_user:accly_password@localhost:5432/accly_db
POSTGRES_READ_REPLICA_URL=postgresql://accly_user:accly_password@localhost:5433/accly_db
POSTGRES_USER=accly_user
POSTGRES_PASSWORD=accly_password
POSTGRES_DB=accly_db

# Redis Configuration
REDIS_URL=redis://:redis_password@localhost:6379
REDIS_PASSWORD=redis_password

# RabbitMQ Configuration
RABBITMQ_URL=amqp://accly_user:accly_password@localhost:5672
RABBITMQ_USER=accly_user
RABBITMQ_PASSWORD=accly_password

# Elasticsearch Configuration
ELASTICSEARCH_URL=http://localhost:9200

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# API Configuration
API_GATEWAY_PORT=3000
AUTH_SERVICE_PORT=3001
USER_SERVICE_PORT=3002
BOOKING_SERVICE_PORT=3003
PAYMENT_SERVICE_PORT=3004
NOTIFICATION_SERVICE_PORT=3005
ANALYTICS_SERVICE_PORT=3006

# Frontend Configuration
NEXT_PUBLIC_API_URL=http://localhost:3000
NEXT_PUBLIC_WS_URL=ws://localhost:3000
FRONTEND_PORT=3100

# Email Service (SendGrid)
SENDGRID_API_KEY=your-sendgrid-api-key
FROM_EMAIL=<EMAIL>
FROM_NAME=Accly Platform

# SMS Service (Twilio)
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=+**********

# Push Notifications (Firebase)
FIREBASE_SERVER_KEY=your-firebase-server-key
FIREBASE_PROJECT_ID=your-firebase-project-id

# Payment Gateways
# Stripe
STRIPE_PUBLISHABLE_KEY=pk_test_your-stripe-publishable-key
STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key
STRIPE_WEBHOOK_SECRET=whsec_your-stripe-webhook-secret

# Razorpay
RAZORPAY_KEY_ID=your-razorpay-key-id
RAZORPAY_KEY_SECRET=your-razorpay-key-secret
RAZORPAY_WEBHOOK_SECRET=your-razorpay-webhook-secret

# PayPal
PAYPAL_CLIENT_ID=your-paypal-client-id
PAYPAL_CLIENT_SECRET=your-paypal-client-secret
PAYPAL_MODE=sandbox

# File Storage (AWS S3)
AWS_ACCESS_KEY_ID=your-aws-access-key-id
AWS_SECRET_ACCESS_KEY=your-aws-secret-access-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=accly-documents
AWS_CLOUDFRONT_DOMAIN=your-cloudfront-domain.cloudfront.net

# Google Services
GOOGLE_MAPS_API_KEY=your-google-maps-api-key
GOOGLE_OAUTH_CLIENT_ID=your-google-oauth-client-id
GOOGLE_OAUTH_CLIENT_SECRET=your-google-oauth-client-secret

# Third-party Integrations
# QuickBooks
QUICKBOOKS_CLIENT_ID=your-quickbooks-client-id
QUICKBOOKS_CLIENT_SECRET=your-quickbooks-client-secret
QUICKBOOKS_REDIRECT_URI=http://localhost:3000/auth/quickbooks/callback

# Xero
XERO_CLIENT_ID=your-xero-client-id
XERO_CLIENT_SECRET=your-xero-client-secret
XERO_REDIRECT_URI=http://localhost:3000/auth/xero/callback

# Video Calling (Agora or Zoom)
AGORA_APP_ID=your-agora-app-id
AGORA_APP_CERTIFICATE=your-agora-app-certificate

# Monitoring and Logging
SENTRY_DSN=your-sentry-dsn
NEW_RELIC_LICENSE_KEY=your-new-relic-license-key
DATADOG_API_KEY=your-datadog-api-key

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Security
BCRYPT_ROUNDS=12
CORS_ORIGIN=http://localhost:3100
COOKIE_SECRET=your-cookie-secret-key

# Application Settings
NODE_ENV=development
LOG_LEVEL=debug
API_VERSION=v1
MAX_FILE_SIZE=********
ALLOWED_FILE_TYPES=pdf,doc,docx,jpg,jpeg,png

# Caching
CACHE_TTL=3600
SESSION_TTL=86400

# Database Connection Pool
DB_POOL_MIN=2
DB_POOL_MAX=10
DB_POOL_IDLE_TIMEOUT=30000

# WhatsApp Business API
WHATSAPP_BUSINESS_ACCOUNT_ID=your-whatsapp-business-account-id
WHATSAPP_ACCESS_TOKEN=your-whatsapp-access-token
WHATSAPP_PHONE_NUMBER_ID=your-whatsapp-phone-number-id
WHATSAPP_WEBHOOK_VERIFY_TOKEN=your-whatsapp-webhook-verify-token

# AI/ML Services
OPENAI_API_KEY=your-openai-api-key
OPENAI_MODEL=gpt-3.5-turbo

# Backup Configuration
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=accly-backups

# Load Testing
LOAD_TEST_CONCURRENT_USERS=1000
LOAD_TEST_DURATION=300s
LOAD_TEST_RAMP_UP=60s

# Feature Flags
ENABLE_AI_CHATBOT=true
ENABLE_VIDEO_CALLS=true
ENABLE_WHATSAPP_NOTIFICATIONS=true
ENABLE_ANALYTICS_DASHBOARD=true
ENABLE_MOBILE_APP=true

# Kubernetes Configuration (for production)
K8S_NAMESPACE=accly-production
K8S_CLUSTER_NAME=accly-cluster
K8S_INGRESS_CLASS=nginx

# SSL/TLS Configuration
SSL_CERT_PATH=/etc/ssl/certs/accly.crt
SSL_KEY_PATH=/etc/ssl/private/accly.key

# Health Check Configuration
HEALTH_CHECK_INTERVAL=30s
HEALTH_CHECK_TIMEOUT=10s
HEALTH_CHECK_RETRIES=3
